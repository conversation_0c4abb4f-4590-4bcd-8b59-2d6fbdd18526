# NOTE: This refactored worker uses aioboto3. You may need to install it: pip install aioboto3
"""
Worker to process S3 events from an SQS queue concurrently using asyncio.
"""
import sys
import json
import time
import asyncio
from io import BytesIO
import aioboto3
from botocore.exceptions import ClientError
from loguru import logger
import os
from pathlib import Path
from typing import Dict, Any, List

# Add project root to the Python path to allow for absolute imports
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(project_root))

from app.core.configuration import settings
from app.services.s3_service import s3_service
from app.llm.extraction import main as llm_main


class S3EventProcessor:
    """
    A worker that processes S3 upload events from an SQS queue concurrently.
    """
    def __init__(self, queue_url: str, region_name: str):
        if not queue_url:
            raise ValueError("SQS_QUEUE_URL must be configured in your .env file to run the worker.")
        
        self.aws_access_key_id = settings.AWS_ACCESS_KEY_ID or None
        self.aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY or None
        self.output_folder = settings.EXTRACTION_FOLDER_OUTPUT_PREFIX
        self.queue_url = queue_url
        self.region_name = region_name
        logger.info(f"Async Worker initialized for SQS queue: {queue_url}")

    async def process_messages(self):
        """
        Continuously polls the SQS queue and processes messages concurrently.
        """
        logger.info("Starting concurrent S3 event processor worker...")
        session = aioboto3.Session(
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.region_name
        )
        async with session.client("sqs") as sqs_client:
            while True:
                try:
                    logger.debug("Polling for messages...")
                    response = await sqs_client.receive_message(
                        QueueUrl=self.queue_url,
                        MaxNumberOfMessages=10,  # Fetch up to 10 messages
                        WaitTimeSeconds=20,      # Use long polling
                        MessageAttributeNames=['All']
                    )

                    messages = response.get('Messages', [])
                    if not messages:
                        logger.debug("No new messages. Waiting...")
                        continue
                    
                    logger.info(f"Fetched {len(messages)} messages. Processing concurrently...")
                    
                    # Create and run tasks for each message concurrently
                    processing_tasks = [
                        self._process_single_message(message, sqs_client) for message in messages
                    ]
                    await asyncio.gather(*processing_tasks)

                except ClientError as e:
                    logger.error(f"SQS client error: {e}")
                    await asyncio.sleep(10) # Wait before retrying on client errors

    async def _process_single_message(self, message: Dict[str, Any], sqs_client):
        """Processes all records in a single SQS message and deletes it upon success."""
        receipt_handle = message['ReceiptHandle']
        try:
            logger.info(f"Processing message: {message['MessageId']}")
            body = json.loads(message['Body'])
            s3_event = json.loads(body['Message']) if 'Message' in body else body

            if 'Records' not in s3_event:
                logger.warning(f"Message {message['MessageId']} is not a valid S3 event. Skipping.")
                await self._delete_message(receipt_handle, sqs_client)
                return

            # Concurrently process all records within the single SQS message
            ingestion_tasks = []
            for record in s3_event['Records']:
                bucket_name = record['s3']['bucket']['name']
                object_key = record['s3']['object']['key']
                logger.info(f"Queueing processing for file: s3://{bucket_name}/{object_key}")
                task = asyncio.create_task(self._handle_ingestion(bucket_name, object_key))
                ingestion_tasks.append(task)
            
            # Wait for all records in the message to be processed
            await asyncio.gather(*ingestion_tasks)

            # If all records processed successfully, delete the message from the queue
            await self._delete_message(receipt_handle, sqs_client)

        except Exception as e:
            # If any record fails, the entire message processing fails.
            # The exception is logged, and the message is NOT deleted, allowing SQS to retry.
            logger.error(f"Failed to process message {message['MessageId']}. It will be retried. Error: {e}")

    async def _handle_ingestion(self, bucket_name: str, object_key: str):
        """
        Triggers ingestion for a single file and uploads the resulting JSON.
        This function is now async and meant to be run as a concurrent task.
        """
        try:
            s3_uri = f"s3://{bucket_name}/{object_key}"
            # Await the async LLM function directly
            json_response = await llm_main(s3_uri=s3_uri)

            logger.info(f"LLM processing complete for {object_key}. Response: {json_response}")

            if not json_response:
                logger.warning(f"LLM process returned no data for {object_key}. Skipping JSON upload.")
                return

            json_response["s3_object_key"] = object_key

            # Remove the first two levels of the directory structure (e.g., "input/extraction-input/")
            path_components = object_key.split('/')
            if len(path_components) > 2:
                path_after_prefix = '/'.join(path_components[2:])
            else:
                path_after_prefix = path_components[-1]

            base_path, _ = os.path.splitext(path_after_prefix)
            output_json_key = f"{self.output_folder}/{base_path}.json"

            logger.info(f"Uploading processed JSON to s3://{bucket_name}/{output_json_key}")

            json_bytes = json.dumps(json_response, indent=4).encode('utf-8')
            
            # Run the synchronous s3_client.upload_fileobj in a separate thread
            # to avoid blocking the asyncio event loop.
            await asyncio.to_thread(
                s3_service.s3_client.upload_fileobj,
                Fileobj=BytesIO(json_bytes),
                Bucket=bucket_name,
                Key=output_json_key,
                ExtraArgs={'ContentType': 'application/json'}
            )

            logger.success(f"Successfully processed and uploaded result for {s3_uri}")

        except Exception as e:
            logger.error(f"Failed to ingest {s3_uri}: {e}")
            raise  # Re-raise to be caught by _process_single_message

    async def _delete_message(self, receipt_handle: str, sqs_client):
        """Asynchronously delete a message from the SQS queue."""
        try:
            await sqs_client.delete_message(QueueUrl=self.queue_url, ReceiptHandle=receipt_handle)
            logger.info(f"Message with handle {receipt_handle[:10]}... deleted.")
        except ClientError as e:
            logger.error(f"Failed to delete message: {e}")


if __name__ == "__main__":
    # This allows running the worker as a standalone script.
    # From the project root, run: python -m app.services.s3_event_processor
    worker = S3EventProcessor(queue_url=settings.SQS_EXTRACTION_QUEUE_URL, region_name=settings.AWS_REGION)
    asyncio.run(worker.process_messages())
