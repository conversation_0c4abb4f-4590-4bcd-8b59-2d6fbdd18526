"""
Worker to process S3 events from an SQS queue.
"""
import sys
import json
import time
from io import BytesIO
import boto3
from botocore.exceptions import ClientError
from loguru import logger
import os
from pathlib import Path
from pypdf import PdfReader, PdfWriter

# Add project root to the Python path to allow for absolute imports
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(project_root))

from app.core.configuration import settings
from app.services.s3_service import s3_service
from app.llm.bda import main as llm_main
# NOTE: You would import your ingestion service here once it's created.
# from app.services.ingestion_service import process_document


class S3EventProcessor:
    """
    A worker that processes S3 upload events from an SQS queue.
    """
    def __init__(self, queue_url: str, region_name: str):
        if not queue_url:
            raise ValueError("SQS_QUEUE_URL must be configured in your .env file to run the worker.")
        
        # Pass None instead of empty strings to allow boto3 to use its credential provider chain
        aws_access_key_id = settings.AWS_ACCESS_KEY_ID or None
        aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY or None

        self.sqs_client = boto3.client(
            "sqs",
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            region_name=region_name
        )
        self.queue_url = queue_url
        logger.info(f"Worker initialized for SQS queue: {queue_url}")

    def process_messages(self):
        """
        Continuously poll the SQS queue for messages and process them.
        """
        logger.info("Starting S3 event processor worker...")
        while True:
            try:
                # Use long-polling to efficiently wait for messages
                response = self.sqs_client.receive_message(
                    QueueUrl=self.queue_url,
                    MaxNumberOfMessages=10,  # Process up to 10 messages at a time
                    WaitTimeSeconds=20,      # Enable long polling
                    MessageAttributeNames=['All']
                )

                messages = response.get('Messages', [])
                if not messages:
                    logger.debug("No new messages. Waiting...")
                    continue

                for message in messages:
                    receipt_handle = message['ReceiptHandle']
                    try:
                        logger.info(f"Received message: {message['MessageId']}")
                        body = json.loads(message['Body'])
                        
                        # S3 event messages might be wrapped in an SNS format
                        s3_event = json.loads(body['Message']) if 'Message' in body else body

                        if 'Records' not in s3_event:
                            logger.warning("Message is not a valid S3 event. Skipping.")
                            self._delete_message(receipt_handle)
                            continue

                        for record in s3_event['Records']:
                            bucket_name = record['s3']['bucket']['name']
                            object_key = record['s3']['object']['key']
                            
                            logger.info(f"Processing file: s3://{bucket_name}/{object_key}")
                            
                            # This is where you would call your ingestion logic
                            self._handle_ingestion(bucket_name, object_key)

                        # Delete the message from the queue after successful processing
                        self._delete_message(receipt_handle)

                    except Exception as e:
                        logger.error(f"Error processing message {message['MessageId']}: {e}")
                        # Do not delete the message, let it be re-processed after visibility timeout

            except ClientError as e:
                logger.error(f"SQS client error: {e}")
                time.sleep(10) # Wait before retrying

    def _handle_ingestion(self, bucket_name: str, object_key: str):
        """
        Triggers ingestion for a file, classifies it, splits the PDF, and uploads the results.

        This function calls the main LLM processing logic to classify the document.
        Based on the classification, the original PDF is split into multiple documents,
        which are then uploaded back to a designated folder in S3.
        """
        try:
            s3_uri = f"s3://{bucket_name}/{object_key}"
            output_s3_uri = f"s3://{bucket_name}/output/"

            logger.info(f"Triggering ingestion for {s3_uri} to {output_s3_uri}")

            json_response = llm_main(input_s3_uri=s3_uri, output_s3_uri=output_s3_uri)

            logger.info(f"LLM processing complete for {object_key}. Response: {json_response}")

            if not json_response or "documents" not in json_response:
                logger.warning(f"LLM process returned no valid data for {object_key}. Skipping PDF splitting.")
                return

            # Download the original PDF to memory
            pdf_file_object = BytesIO()
            s3_service.s3_client.download_fileobj(bucket_name, object_key, pdf_file_object)
            pdf_file_object.seek(0)

            pdf_reader = PdfReader(pdf_file_object)
            input_path = Path(object_key)

            for doc in json_response["documents"]:
                doc_type = doc.get("doc_type", "unknown")
                
                # Create a new PDF writer for each document
                pdf_writer = PdfWriter()
                
                page_numbers = [p["page_number"] for p in doc.get("pages", [])]
                if not page_numbers:
                    logger.warning(f"No pages found for doc_type {doc_type} in {object_key}")
                    continue

                for page_num in page_numbers:
                    if 1 <= page_num <= len(pdf_reader.pages):
                        pdf_writer.add_page(pdf_reader.pages[page_num - 1])
                
                if len(pdf_writer.pages) > 0:
                    # Construct the output key
                    page_info = f"p{page_numbers[0]}" if len(page_numbers) == 1 else f"p{page_numbers[0]}-{page_numbers[-1]}"
                    output_filename = f"{input_path.stem}_{doc_type}_{page_info}.pdf"
                    output_folder = "output/document-classification-output"
                    output_pdf_key = f"{output_folder}/{output_filename}"

                    # Write the new PDF to a bytes buffer
                    output_pdf_buffer = BytesIO()
                    pdf_writer.write(output_pdf_buffer)
                    output_pdf_buffer.seek(0)

                    logger.info(f"Uploading split PDF to s3://{bucket_name}/{output_pdf_key}")

                    # Upload the new PDF to S3
                    s3_service.s3_client.upload_fileobj(
                        Fileobj=output_pdf_buffer,
                        Bucket=bucket_name,
                        Key=output_pdf_key,
                        ExtraArgs={'ContentType': 'application/pdf'}
                    )

            logger.success(f"Successfully processed and split {s3_uri}")

        except Exception as e:
            logger.error(f"Failed to ingest {s3_uri}: {e}")
            # Re-raise the exception to allow for retries
            raise

    def _delete_message(self, receipt_handle: str):
        """Delete a message from the SQS queue."""
        try:
            self.sqs_client.delete_message(QueueUrl=self.queue_url, ReceiptHandle=receipt_handle)
            logger.info(f"Message with handle {receipt_handle[:10]}... deleted.")
        except ClientError as e:
            logger.error(f"Failed to delete message: {e}")


if __name__ == "__main__":
    # This allows running the worker as a standalone script.
    # From the project root, run: python -m app.services.s3_event_processor
    worker = S3EventProcessor(queue_url=settings.SQS_CLASSIFICATION_QUEUE_URL, region_name=settings.AWS_REGION)
    worker.process_messages()