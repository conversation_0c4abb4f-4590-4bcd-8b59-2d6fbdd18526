
import sys
import boto3
import time
import json
import os
from datetime import datetime
from pathlib import Path
from botocore.exceptions import Client<PERSON>rror

# Add project root to the Python path to allow for absolute imports
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(project_root))
from app.core.configuration import settings


class BDAProcessor:
    """
    BDA processor with data extraction capabilities.
    """
    def __init__(self):
        print(f"🚀 Initializing BDAProcessor...")

        try:
            self.bda_client = boto3.client('bedrock-data-automation-runtime', 
                                           region_name=settings.AWS_REGION, 
                                           aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                                           aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
                                )
            self.s3_client = boto3.client('s3', 
                                          region_name=settings.AWS_REGION,
                                          aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                                          aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
                                )
            self.sts_client = boto3.client('sts',
                                           region_name=settings.AWS_REGION,
                                           aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                                           aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
                                )
            self.region = settings.AWS_REGION
            self.aws_account_id = self.sts_client.get_caller_identity().get('Account')

            print(f"✅ Successfully initialized BDAProcessor")
            print(f"   AWS Account ID: {self.aws_account_id}")
            print(f"   Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        except Exception as e:
            print(f"❌ Failed to initialize BDAProcessor: {e}")
            raise

    def start_processing(self, input_s3_uri: str, output_s3_uri: str) -> str:
        """
        Start BDA processing for a file in S3. Returns invocation ARN.
        """
        print(f"\n📤 Starting BDA Processing...")
        print(f"   Input S3 URI: {input_s3_uri}")
        print(f"   Output S3 URI: {output_s3_uri}")
        print(f"   Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        try:
            profile_arn = f"arn:aws:bedrock:{self.region}:{self.aws_account_id}:data-automation-profile/us.data-automation-v1"
            blueprint_arn = f"arn:aws:bedrock:{self.region}:aws:blueprint/bedrock-data-automation-public-invoice"

            response = self.bda_client.invoke_data_automation_async(
                inputConfiguration={'s3Uri': input_s3_uri},
                outputConfiguration={'s3Uri': output_s3_uri},
                dataAutomationProfileArn=profile_arn,
                blueprints=[
                    {
                        "blueprintArn": blueprint_arn
                    }
                ]
            )

            invocation_arn = response.get('invocationArn')
            print(f"✅ BDA Processing Started Successfully!")
            print(f"   Invocation ARN: {invocation_arn}")

            return invocation_arn

        except ClientError as e:
            print(f"❌ Failed to start BDA processing: {e}")
            raise
        except Exception as e:
            print(f"❌ Unexpected error during BDA processing: {e}")
            raise

    def get_result(self, invocation_arn: str) -> dict:
        """
        Polls for job status and returns result dict when available.
        """
        print(f"\n⏳ Monitoring BDA Job Status...")
        print(f"   Invocation ARN: {invocation_arn}")
        print(f"   Started monitoring at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        poll_count = 0
        start_time = datetime.now()

        try:
            while True:
                poll_count += 1
                status_response = self.bda_client.get_data_automation_status(invocationArn=invocation_arn)
                status = status_response.get('status')

                elapsed_time = (datetime.now() - start_time).total_seconds()
                print(f"   Poll #{poll_count} - Status: {status} (Elapsed: {elapsed_time:.1f}s)")

                if status == 'Success':
                    print(f"✅ BDA Processing Completed Successfully!")
                    print(f"   Total processing time: {elapsed_time:.1f} seconds")
                    print(f"   Total polls: {poll_count}")
                    return status_response
                elif status == 'Failed':
                    print(f"❌ BDA Processing Failed!")
                    print(f"   Total processing time: {elapsed_time:.1f} seconds")
                    print(f"   Total polls: {poll_count}")
                    return status_response
                elif status in ['InProgress', 'Pending']:
                    print(f"   ⏳ Job still {status.lower()}... waiting 5 seconds")
                else:
                    print(f"   ⚠️  Unknown status: {status}")

                time.sleep(5)

        except ClientError as e:
            print(f"❌ Error checking job status: {e}")
            raise
        except Exception as e:
            print(f"❌ Unexpected error while monitoring job: {e}")
            raise

    def extract_s3_path(self, s3_uri: str) -> tuple:
        """Extract bucket and key from S3 URI"""
        if s3_uri.startswith('s3://'):
            parts = s3_uri[5:].split('/', 1)
            bucket = parts[0]
            key = parts[1] if len(parts) > 1 else ''
            return bucket, key
        return None, None

    def get_extracted_data(self, job_result: dict) -> dict:
        """
        Downloads and prints the extracted data from BDA processing results.
        This method should be called after successful processing.

        Args:
            job_result: The result dictionary from get_result() method

        Returns:
            dict: The extracted data from the processed document
        """
        print(f"\n📥 Extracting Processed Data...")

        try:
            # Debug: Print the actual job result structure
            print(f"   Debug - Job result keys: {list(job_result.keys())}")

            # Get job metadata first - handle different response formats
            job_id = job_result.get('job_id') or job_result.get('invocationArn', 'unknown')
            job_status = job_result.get('job_status') or job_result.get('status', 'unknown')

            print(f"   Job ID: {job_id}")
            print(f"   Job Status: {job_status}")

            # Accept both 'PROCESSED' and 'Success' as valid completion statuses
            if job_status not in ['PROCESSED', 'Success']:
                print(f"⚠️  Warning: Job status is '{job_status}', expected 'PROCESSED' or 'Success'")
                if job_status == 'Failed':
                    print(f"❌ Job failed, cannot extract data")
                    return {}

            # Get the output S3 URI from the job result
            output_s3_uri = (job_result.get('outputS3Uri') or
                           job_result.get('outputConfiguration', {}).get('s3Uri') or
                           job_result.get('output', {}).get('s3Uri'))

            if not output_s3_uri:
                print(f"⚠️  No output S3 URI found in job result, attempting to construct it...")

                # Try to construct the output URI from the invocation ARN
                invocation_arn = job_result.get('invocationArn') or job_id
                if invocation_arn and 'data-automation-invocation/' in invocation_arn:
                    # Extract the job ID from the ARN
                    actual_job_id = invocation_arn.split('/')[-1]
                    # Construct the expected output path based on the pattern we've seen
                    output_s3_uri = f"s3://document-extraction-logistically/temp/output//{actual_job_id}/job_metadata.json"
                    print(f"   Constructed output URI: {output_s3_uri}")
                else:
                    print(f"❌ Cannot construct output S3 URI from available data")
                    return {}

            print(f"   Metadata S3 URI: {output_s3_uri}")

            # Download job metadata
            metadata_bucket, metadata_key = self.extract_s3_path(output_s3_uri)
            local_metadata_file = f'/tmp/job_metadata_{job_id}.json'

            print(f"   Downloading job metadata...")
            os.makedirs(os.path.dirname(local_metadata_file), exist_ok=True)
            self.s3_client.download_file(metadata_bucket, metadata_key, local_metadata_file)

            # Parse metadata
            with open(local_metadata_file, 'r') as f:
                metadata = json.load(f)

            print(f"✅ Job metadata downloaded successfully")

            # Extract custom output path from metadata
            if 'output_metadata' in metadata and metadata['output_metadata']:
                asset_metadata = metadata['output_metadata'][0]
                if 'segment_metadata' in asset_metadata and asset_metadata['segment_metadata']:
                    segment = asset_metadata['segment_metadata'][0]
                    custom_output_path = segment.get('custom_output_path')

                    if custom_output_path:
                        print(f"   Found extracted data at: {custom_output_path}")

                        # Download the actual extracted data
                        result_bucket, result_key = self.extract_s3_path(custom_output_path)
                        local_result_file = f'/tmp/extraction_result_{job_id}.json'

                        print(f"   Downloading extracted data...")
                        self.s3_client.download_file(result_bucket, result_key, local_result_file)

                        # Parse and display extracted data
                        with open(local_result_file, 'r') as f:
                            extracted_data = json.load(f)

                        print(f"✅ Extracted data downloaded successfully")

                        # Clean up temporary files
                        os.remove(local_metadata_file)
                        os.remove(local_result_file)

                        return extracted_data
                    else:
                        print(f"❌ No custom output path found in metadata")
                        return {}
            else:
                print(f"❌ No output metadata found in job result")
                return {}

        except ClientError as e:
            print(f"❌ S3 error while extracting data: {e}")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing error: {e}")
            return {}
        except Exception as e:
            print(f"❌ Unexpected error while extracting data: {e}")
            return {}

def main(input_s3_uri ='s3://document-extraction-logistically/temp/11173426_carrier_invoice.pdf',
         output_s3_uri='s3://document-extraction-logistically/temp/output/'):
    """
    Main function to run the enhanced BDA processing pipeline.

    Args:
        input_s3_uri: S3 URI of the input file
        output_s3_uri: S3 URI for the output
        region: AWS region to use

    Returns:
        extracted_data: Extracted data from the processed document
    """
    print(f"🎯 Starting Enhanced BDA Processing Pipeline")
    print(f"   Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"="*80)

    # Configuration
    input_s3_uri = input_s3_uri
    output_s3_uri = output_s3_uri

    try:
        # Initialize processor
        processor = BDAProcessor()

        # Start processing
        invocation_arn = processor.start_processing(input_s3_uri, output_s3_uri)

        # Get results
        result = processor.get_result(invocation_arn)

        # Check if processing was successful
        if result.get('status') == 'Success' or result.get('job_status') == 'PROCESSED':
            print(f"\n🎉 Processing completed successfully!")

            # Extract and print the processed data
            extracted_data = processor.get_extracted_data(result)

            if extracted_data:
                print(f"\n📊 PROCESSING SUMMARY:")
                print(f"   Vendor Name: {extracted_data.get('inference_result', {}).get('VENDORNAME', 'N/A')}")
                print(f"   Invoice Number: {extracted_data.get('inference_result', {}).get('ID', 'N/A')}")
                print(f"   Total amount: ${extracted_data.get('inference_result', {}).get('TOTAL', 'N/A')}")
                print(f"   Invoice Date: {extracted_data.get('inference_result', {}).get('DATE', 'N/A')}")
                return extracted_data
            else: 
                print(f"⚠️  No extracted data available")

        else:
            print(f"\n❌ Processing failed!")
            print(f"   Status: {result.get('status', 'Unknown')}")
            print(f"   Job Status: {result.get('job_status', 'Unknown')}")

    except Exception as e:
        print(f"\n❌ Pipeline failed with error: {e}")
        print(f"   Error time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        raise


if __name__ == "__main__":

    # Sample call

    input_s3_uri ='s3://document-extraction-logistically/temp/11173426_carrier_invoice.pdf'
    main(input_s3_uri)