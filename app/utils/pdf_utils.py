"""
PDF utility functions.
"""
from pathlib import Path
from pypdf import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PdfWriter
from loguru import logger

def split_pdf_to_pages(input_pdf_path: str, output_dir: str) -> None:
    """
    Splits a multi-page PDF into individual single-page PDF files.

    Args:
        input_pdf_path (str): The path to the source multi-page PDF file.
        output_dir (str): The path to the directory where single-page PDFs will be saved.
    """
    try:
        input_path = Path(input_pdf_path)
        if not input_path.is_file():
            logger.error(f"Input file not found: {input_pdf_path}")
            raise FileNotFoundError(f"Input file not found: {input_pdf_path}")

        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"Output directory set to: {output_path.resolve()}")

        pdf_reader = PdfReader(input_pdf_path)
        
        total_pages = len(pdf_reader.pages)
        logger.info(f"Found {total_pages} pages in '{input_path.name}'. Starting split...")

        for page_num, page in enumerate(pdf_reader.pages):
            pdf_writer = PdfWriter()
            pdf_writer.add_page(page)

            output_filename = f"{input_path.stem}_page_{page_num + 1}.pdf"
            output_filepath = output_path / output_filename

            with open(output_filepath, "wb") as out_file:
                pdf_writer.write(out_file)
            
            logger.success(f"Successfully created: {output_filepath}")
        
        logger.info("PDF splitting completed.")

    except Exception as e:
        logger.error(f"An error occurred during PDF splitting: {e}")
        raise

if __name__ == '__main__':
    # This block demonstrates how to use the split_pdf_to_pages function.
    # It first creates a dummy multi-page PDF and then splits it.
    from pypdf import PageObject

    # Create a dummy PDF for testing
    writer = PdfWriter()
    for i in range(5):
        # Create a blank page. A4 size in points is 595 x 842.
        page = PageObject.create_blank_page(width=595, height=842)
        writer.add_page(page)
    
    dummy_pdf_path = "dummy_multipage.pdf"
    with open(dummy_pdf_path, "wb") as f:
        writer.write(f)
    
    logger.info(f"Created a dummy 5-page PDF: {dummy_pdf_path}")

    # Split the dummy PDF
    split_pdf_to_pages(dummy_pdf_path, "split_pages_output")
