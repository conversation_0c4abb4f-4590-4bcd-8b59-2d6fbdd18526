import re
import json
import ast
from typing import Any, Dict

def extract_json_from_backticks(text: str) -> Dict[str, Any]:
    """
    Extract JSON from text - handles both backtick-fenced and plain JSON.
    - First tries to find <PERSON><PERSON><PERSON> enclosed in triple backticks (``` ... ```).
    - If no backticks found, tries to parse the entire text as JSON.
    - Accepts optional fence label (e.g. ```json or ```json\n).
    - Tries json.loads first, then tries extracting {...} or [...] slices,
      then falls back to a best-effort ast.literal_eval after simple literal replacements.
    - Never raises: returns a dict with 'working_json' True and 'data' on success,
      or 'working_json' False with 'error' and the extracted 'content' on failure.

    Returns:
      {'working_json': True, 'data': <parsed object>}
      OR
      {'working_json': False, 'error': 'message', 'content': '<extracted_text (trimmed)>'}
    """
    # First try to find fenced block with triple backticks
    fence_pat = re.compile(r'```(?:[^\n]*)\n?(.*?)```', re.DOTALL)
    m = fence_pat.search(text)

    if m:
        # Found backtick-fenced content
        content = m.group(1)
        stripped = content.strip()
    else:
        # No backticks found, try to parse the entire text as JSON
        stripped = text.strip()

    # 1) Try strict JSON
    try:
        parsed = json.loads(stripped)
        return {'working_json': True, 'data': parsed}
    except json.JSONDecodeError as first_err:
        last_err = str(first_err)

    # 2) Try to locate a JSON object/array substring within the content (first {...} or [...])
    for start_ch, end_ch in (('{', '}'), ('[', ']')):
        s_idx = stripped.find(start_ch)
        e_idx = stripped.rfind(end_ch)
        if s_idx != -1 and e_idx != -1 and e_idx > s_idx:
            candidate = stripped[s_idx:e_idx+1].strip()
            try:
                parsed = json.loads(candidate)
                return {'working_json': True, 'data': parsed}
            except json.JSONDecodeError as e:
                last_err = f"{last_err}; fallback slice JSON error: {e}"

    # 3) Best-effort Python-literal parse (handles single quotes, trailing commas sometimes)
    #    Convert JS literals to Python equivalents (simple replacement).
    #    This may modify inside strings in extreme cases, but it's a pragmatic fallback.
    try:
        alt = re.sub(r'\bnull\b', 'None', stripped, flags=re.IGNORECASE)
        alt = re.sub(r'\btrue\b', 'True', alt, flags=re.IGNORECASE)
        alt = re.sub(r'\bfalse\b', 'False', alt, flags=re.IGNORECASE)

        # Attempt to isolate braces/brackets first (if present)
        s_idx = alt.find('{')
        e_idx = alt.rfind('}')
        if s_idx != -1 and e_idx != -1 and e_idx > s_idx:
            alt_candidate = alt[s_idx:e_idx+1]
        else:
            s_idx = alt.find('[')
            e_idx = alt.rfind(']')
            alt_candidate = alt[s_idx:e_idx+1] if (s_idx != -1 and e_idx != -1 and e_idx > s_idx) else alt

        parsed = ast.literal_eval(alt_candidate)
        return {'working_json': True, 'data': parsed}
    except Exception as e:
        last_err = f"{last_err}; ast fallback error: {e}"

    # If everything fails, return failure with debugging info (trim content for safety)
    debug_content = (stripped[:1000] + '...') if len(stripped) > 1000 else stripped
    return {
        'working_json': False,
        'error': 'Failed to parse JSON from text; last error: ' + last_err,
        'content': debug_content
    }
