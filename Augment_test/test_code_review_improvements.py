#!/usr/bin/env python3
"""
Test file to verify the code review improvements work correctly.

This test file validates:
1. Configuration type fixes
2. Import structure improvements
3. Input validation enhancements
4. Error handling improvements
5. Documentation completeness
"""

import sys
import os
import asyncio
from unittest.mock import Mock, patch, MagicMock

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.configuration import settings
from app.llm.extraction import DocumentExtractionProcessor
from app.llm.classification import process_document
from app.utils.textract import TextractProcessor, parse_s3_uri
from app.llm.bedrock import BedrockProcessor
from app.utils.langfuse_util import LangfuseUtil


class TestConfigurationImprovements:
    """Test configuration type fixes and improvements."""
    
    def test_configuration_types(self):
        """Test that configuration values have correct types."""
        # Test integer types
        assert isinstance(settings.PORT, int)
        assert isinstance(settings.LANGFUSE_FLUSH_AT, int)
        assert isinstance(settings.LANGFUSE_FLUSH_INTERVAL, int)
        
        # Test boolean types
        assert isinstance(settings.SHOW_SWAGGER_DOC, bool)
        assert isinstance(settings.LANGCHAIN_TRACING_V2, bool)
        assert isinstance(settings.LANGFUSE_ENABLED, bool)
        
        # Test list types
        assert isinstance(settings.BACKEND_CORS_ORIGINS, list)
        
        # Test string types (can be None for optional fields)
        assert settings.PROJECT_NAME is None or isinstance(settings.PROJECT_NAME, str)
        assert settings.HOST is None or isinstance(settings.HOST, str)


class TestInputValidation:
    """Test enhanced input validation."""
    
    def test_s3_uri_validation(self):
        """Test S3 URI validation improvements."""
        # Test valid S3 URI parsing
        bucket, key = parse_s3_uri("s3://test-bucket/test-key.pdf")
        assert bucket == "test-bucket"
        assert key == "test-key.pdf"
        
        # Test invalid S3 URIs
        try:
            parse_s3_uri("http://test-bucket/test-key.pdf")
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "Invalid S3 URI scheme" in str(e)

        try:
            parse_s3_uri("s3:///test-key.pdf")
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "Bucket name is missing" in str(e)

        try:
            parse_s3_uri("s3://test-bucket/")
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "Object key is missing" in str(e)
    
    def test_extraction_input_validation(self):
        """Test extraction processor input validation."""
        processor = DocumentExtractionProcessor()

        # Test invalid S3 URI - empty string
        try:
            asyncio.run(processor.process_document_extraction(""))
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "s3_uri must be a non-empty string" in str(e)

        # Test invalid S3 URI - wrong protocol
        try:
            asyncio.run(processor.process_document_extraction("http://bucket/key"))
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "s3_uri must start with 's3://'" in str(e)

        # Test invalid S3 URI - too short
        try:
            asyncio.run(processor.process_document_extraction("s3://"))
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "s3_uri is too short" in str(e)

    def test_bedrock_parameter_validation(self):
        """Test Bedrock parameter validation."""
        processor = DocumentExtractionProcessor()

        # Test invalid temperature
        try:
            asyncio.run(processor.extract_data_with_bedrock(
                system_prompt="test",
                user_prompt="test",
                temperature=3.0
            ))
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "temperature must be a number between 0 and 2" in str(e)

        # Test invalid top_p
        try:
            asyncio.run(processor.extract_data_with_bedrock(
                system_prompt="test",
                user_prompt="test",
                top_p=1.5
            ))
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "top_p must be a number between 0 and 1" in str(e)

        # Test invalid max_tokens
        try:
            asyncio.run(processor.extract_data_with_bedrock(
                system_prompt="test",
                user_prompt="test",
                max_tokens=-100
            ))
            assert False, "Should have raised ValueError"
        except ValueError as e:
            assert "max_tokens must be a positive integer" in str(e)


class TestResourceManagement:
    """Test resource management improvements."""
    
    def test_langfuse_shutdown(self):
        """Test improved Langfuse shutdown mechanism."""
        langfuse_util = LangfuseUtil()
        
        # Mock the langfuse client
        mock_client = Mock()
        langfuse_util.langfuse = mock_client
        
        # Test shutdown
        langfuse_util.shutdown()
        
        # Verify shutdown was called and reference cleared
        mock_client.shutdown.assert_called_once()
        assert langfuse_util.langfuse is None
    
    def test_langfuse_shutdown_with_error(self):
        """Test Langfuse shutdown handles errors gracefully."""
        langfuse_util = LangfuseUtil()
        
        # Mock the langfuse client to raise an error
        mock_client = Mock()
        mock_client.shutdown.side_effect = Exception("Shutdown error")
        langfuse_util.langfuse = mock_client
        
        # Test shutdown doesn't raise
        langfuse_util.shutdown()
        
        # Verify reference is still cleared despite error
        assert langfuse_util.langfuse is None


class TestPerformanceOptimizations:
    """Test performance optimization improvements."""
    
    def test_textract_constants(self):
        """Test that performance constants are properly defined."""
        from app.utils.textract import (
            DEFAULT_MAX_WORKERS, 
            DEFAULT_POLL_INTERVAL, 
            DEFAULT_MAX_WAIT_TIME,
            TEXTRACT_MAX_FILE_SIZE
        )
        
        assert isinstance(DEFAULT_MAX_WORKERS, int)
        assert DEFAULT_MAX_WORKERS > 0
        assert isinstance(DEFAULT_POLL_INTERVAL, int)
        assert DEFAULT_POLL_INTERVAL > 0
        assert isinstance(DEFAULT_MAX_WAIT_TIME, int)
        assert DEFAULT_MAX_WAIT_TIME > 0
        assert isinstance(TEXTRACT_MAX_FILE_SIZE, int)
        assert TEXTRACT_MAX_FILE_SIZE > 0


class TestDocumentationCompleteness:
    """Test that documentation improvements are in place."""
    
    def test_class_docstrings(self):
        """Test that main classes have comprehensive docstrings."""
        # Test DocumentExtractionProcessor
        assert DocumentExtractionProcessor.__doc__ is not None
        assert "Production-ready" in DocumentExtractionProcessor.__doc__
        assert "Example:" in DocumentExtractionProcessor.__doc__
        
        # Test BedrockProcessor
        assert BedrockProcessor.__doc__ is not None
        
        # Test TextractProcessor
        assert TextractProcessor.__doc__ is not None
    
    def test_method_docstrings(self):
        """Test that key methods have detailed docstrings."""
        # Test main processing method
        method_doc = DocumentExtractionProcessor.process_document_extraction.__doc__
        assert method_doc is not None
        assert "Args:" in method_doc
        assert "Returns:" in method_doc
        assert "Raises:" in method_doc
        
        # Test Bedrock extraction method
        method_doc = DocumentExtractionProcessor.extract_data_with_bedrock.__doc__
        assert method_doc is not None
        assert "comprehensive validation" in method_doc.lower()


def run_tests():
    """Run all tests and report results."""
    print("Running code review improvement tests...")
    
    # Run the tests
    test_config = TestConfigurationImprovements()
    test_validation = TestInputValidation()
    test_resources = TestResourceManagement()
    test_performance = TestPerformanceOptimizations()
    test_docs = TestDocumentationCompleteness()
    
    try:
        # Configuration tests
        test_config.test_configuration_types()
        print("✓ Configuration type improvements verified")
        
        # Input validation tests
        test_validation.test_s3_uri_validation()
        print("✓ S3 URI validation improvements verified")
        
        # Resource management tests
        test_resources.test_langfuse_shutdown()
        test_resources.test_langfuse_shutdown_with_error()
        print("✓ Resource management improvements verified")
        
        # Performance tests
        test_performance.test_textract_constants()
        print("✓ Performance optimization constants verified")
        
        # Documentation tests
        test_docs.test_class_docstrings()
        test_docs.test_method_docstrings()
        print("✓ Documentation improvements verified")
        
        print("\n🎉 All code review improvements verified successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
